{"cells": [{"cell_type": "code", "source": ["# Required Libraries:\n", "# pip install pandas numpy mat<PERSON><PERSON>lib seaborn scikit-learn statsmodels pmdarima\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "from pmdarima import auto_arima\n", "from statsmodels.tsa.statespace.sarimax import SARIMAX\n", "\n", "# Suppress warnings for cleaner output, especially from pmdarima\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "print(\"Starting Electric Demand Forecasting Program...\")\n", "\n", "# --- 1. Load and Preprocess Data ---\n", "print(\"\\n--- 1. Data Loading and Preprocessing ---\")\n", "\n", "# Load the dataset\n", "try:\n", "    df = pd.read_csv('electric_demand_1h.csv')\n", "    print(\"Dataset 'electric_demand_1h.csv' loaded successfully.\")\n", "except FileNotFoundError:\n", "    print(\"Error: 'electric_demand_1h.csv' not found. Please ensure the file is in the same directory as the script.\")\n", "    exit() # Exit if the file is not found\n", "\n", "# Combine date and time columns to create a single datetime index\n", "print(\"Creating datetime index from 'Year', 'Month', 'Day', 'Hour' columns...\")\n", "df['datetime'] = pd.to_datetime(df[['Year', 'Month', 'Day', 'Hour']])\n", "df.set_index('datetime', inplace=True)\n", "\n", "# Sort by index to ensure chronological order, critical for time series\n", "df.sort_index(inplace=True)\n", "\n", "# Set the frequency of the time series index to hourly ('H')\n", "# This helps statsmodels correctly handle time series operations and seasonality.\n", "df.index.freq = 'H'\n", "\n", "# Confirm no missing values (as per user's input)\n", "print(\"Checking for missing values across all columns:\")\n", "print(df.isnull().sum())\n", "if df.isnull().sum().any():\n", "    print(\"Warning: Missing values detected despite user confirmation. Please review your data.\")\n", "\n", "# Define the target variable\n", "target_variable = 'demand'\n", "\n", "# Prepare exogenous variables\n", "print(\"Preparing exogenous variables: 'Temperature', 'WeekDay', 'Holiday'...\")\n", "\n", "# One-hot encode WeekDay. This creates new columns like WeekDay_1, WeekDay_2, etc.\n", "# Dropping the first category to avoid multicollinearity (e.g., WeekDay_1 will be represented when all others are 0).\n", "weekday_dummies = pd.get_dummies(df['WeekDay'], prefix='WeekDay', drop_first=True)\n", "\n", "# One-hot encode Holiday. 'Holiday_1' indicates a holiday. 'Holiday_0' is implied when 'Holiday_1' is 0.\n", "holiday_dummy = pd.get_dummies(df['Holiday'], prefix='Holiday', drop_first=True)\n", "\n", "# Combine all exogenous variables into a single DataFrame.\n", "# Ensure the index matches the main DataFrame for correct alignment during modeling.\n", "exog_vars_df = df[['Temperature']].copy() # Start with Temperature\n", "exog_vars_df = pd.concat([exog_vars_df, weekday_dummies, holiday_dummy], axis=1)\n", "\n", "# Display the first few rows of the processed exogenous variables for review\n", "print(\"\\nFirst 5 rows of processed exogenous variables:\")\n", "print(exog_vars_df.head())\n", "\n", "# Combine the target variable and exogenous variables into a single DataFrame for splitting\n", "df_processed = pd.concat([df[target_variable], exog_vars_df], axis=1)\n", "\n", "\n", "# --- 2. Data Splitting (70% Train, 20% Test, 10% Validation) ---\n", "print(\"\\n--- 2. Data Splitting ---\")\n", "\n", "total_rows = len(df_processed)\n", "train_size = int(0.7 * total_rows)\n", "test_size = int(0.2 * total_rows)\n", "# The remaining rows constitute the validation set to ensure exact splitting.\n", "validation_size = total_rows - train_size - test_size\n", "\n", "train_df = df_processed.iloc[:train_size]\n", "test_df = df_processed.iloc[train_size : train_size + test_size]\n", "validation_df = df_processed.iloc[train_size + test_size :]\n", "\n", "print(f\"Total data points: {total_rows}\")\n", "print(f\"Train set size (70%): {len(train_df)} rows\")\n", "print(f\"Test set size (20%): {len(test_df)} rows\")\n", "print(f\"Validation set size (10%): {len(validation_df)} rows\")\n", "\n", "# Define target and exogenous variables for each split\n", "y_train = train_df[target_variable]\n", "exog_train = train_df.drop(columns=[target_variable])\n", "\n", "y_test = test_df[target_variable]\n", "exog_test = test_df.drop(columns=[target_variable])\n", "\n", "y_validation = validation_df[target_variable]\n", "exog_validation = validation_df.drop(columns=[target_variable])\n", "\n", "\n", "# --- 3. SARIMAX Model Training (using pmdarima.auto_arima) ---\n", "print(\"\\n--- 3. SARIMAX Model Training (using auto_arima) ---\")\n", "print(\"Finding optimal SARIMAX parameters with exogenous variables. This may take some time...\")\n", "print(\"Considering daily seasonality (m=24). WeekDay and Holiday will capture weekly/holiday effects.\")\n", "\n", "# Using auto_arima to find the best SARIMAX (Seasonal ARIMA with eXogenous variables) parameters.\n", "# - y: The target time series.\n", "# - exogenous: DataFrame of exogenous variables.\n", "# - m: The length of the seasonal cycle (24 for daily seasonality in hourly data).\n", "# - d, D: Differencing orders. None means auto_arima will determine non-seasonal differencing. D=1 enforces seasonal differencing.\n", "# - start_p, start_q, max_p, max_q: Range for non-seasonal AR and MA orders.\n", "# - start_P, start_Q, max_P, max_Q: Range for seasonal AR and MA orders.\n", "# - trace=True: Prints the search progress.\n", "# - error_action='ignore': Ignores warnings from unsuitable models during search.\n", "# - suppress_warnings=True: Suppresses all warnings (generally not recommended in production, but good for clean output).\n", "# - stepwise=True: Uses a faster stepwise search (recommended for auto_arima).\n", "# - n_jobs=-1: Uses all available CPU cores for parallel processing, speeding up search.\n", "try:\n", "    smodel = auto_arima(y=y_train,\n", "                        exogenous=exog_train,\n", "                        start_p=1, start_q=1,\n", "                        max_p=7, max_q=7, # Increased max_p/q for richer model\n", "                        start_P=0, start_Q=0,\n", "                        max_P=3, max_Q=3, # Increased max_P/Q for potential seasonal patterns\n", "                        m=24,            # Daily seasonality (24 hours)\n", "                        d=None, D=1,     # Let auto_arima determine d, enforce D=1 for seasonal differencing\n", "                        trace=True,      # Show search progress\n", "                        error_action='ignore', # Ignore errors\n", "                        suppress_warnings=True, # Suppress all warnings\n", "                        stepwise=True,   # Use stepwise search\n", "                        n_jobs=-1)       # Use all CPU cores for faster search\n", "    print(\"\\n--- auto_arima Summary of Optimal Model ---\")\n", "    print(smodel.summary())\n", "except Exception as e:\n", "    print(f\"\\nAn error occurred during auto_arima search: {e}\")\n", "    print(\"Attempting auto_arima with a more constrained search space (max_order=3, n_jobs=1) for robustness.\")\n", "    smodel = auto_arima(y=y_train,\n", "                        exogenous=exog_train,\n", "                        start_p=1, start_q=1,\n", "                        max_p=3, max_q=3,\n", "                        start_P=0, start_Q=0,\n", "                        max_P=2, max_Q=2,\n", "                        m=24,\n", "                        d=None, D=1,\n", "                        trace=True,\n", "                        error_action='ignore',\n", "                        suppress_warnings=True,\n", "                        stepwise=True,\n", "                        n_jobs=1) # Reduced n_jobs to 1 for stability\n", "    print(\"\\n--- auto_arima Summary of Optimal Model (Constrained Search) ---\")\n", "    print(smodel.summary())\n", "\n", "print(f\"\\nOptimal SARIMAX orders found by auto_arima:\")\n", "print(f\"Non-seasonal order (p,d,q): {smodel.order}\")\n", "print(f\"Seasonal order (P,D,Q,s): {smodel.seasonal_order}\")\n", "\n", "\n", "# --- 4. <PERSON><PERSON><PERSON> on Testing Set ---\n", "print(\"\\n--- 4. Forecasting and Evaluation on Testing Set ---\")\n", "\n", "# Ensure exogenous variables for testing are aligned with the test set's time index\n", "if not exog_test.index.equals(y_test.index):\n", "    print(\"Error: Exogenous variables index for test set does not match target variable index.\")\n", "    # Attempt to re-align or raise an error\n", "    exog_test = exog_test.reindex(y_test.index)\n", "\n", "# Forecast on the test set using the fitted auto_arima model\n", "# The n_periods parameter determines how many steps to forecast.\n", "try:\n", "    test_forecast = smodel.predict(n_periods=len(y_test), exogenous=exog_test)\n", "    test_forecast.index = y_test.index # Ensure index matches actual for plotting and metrics\n", "\n", "    # Calculate evaluation metrics for Test Set\n", "    rmse_test = np.sqrt(mean_squared_error(y_test, test_forecast))\n", "    mae_test = mean_absolute_error(y_test, test_forecast)\n", "    # MAPE needs to handle potential division by zero. Add a small epsilon (1e-8) to avoid it.\n", "    mape_test = np.mean(np.abs((y_test - test_forecast) / (y_test + 1e-8))) * 100\n", "\n", "    print(f\"\\n--- Performance on Testing Set ---\")\n", "    print(f\"RMSE: {rmse_test:.2f}\")\n", "    print(f\"MAE: {mae_test:.2f}\")\n", "    print(f\"MAPE: {mape_test:.2f}%\")\n", "\n", "    # Plot Test Set Forecast\n", "    plt.figure(figsize=(18, 7))\n", "    plt.plot(y_train.index[-24*7:], y_train[-24*7:], label='Last Week of Training Data', color='blue', alpha=0.7) # Show last week of training for context\n", "    plt.plot(y_test.index, y_test, label='Actual Test Data', color='green')\n", "    plt.plot(test_forecast.index, test_forecast, label='SARIMAX Test Forecast', color='red', linestyle='--')\n", "    plt.title('SARIMAX Forecast vs Actual on Testing Set')\n", "    plt.xlabel('Date-Time')\n", "    plt.ylabel('Demand')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "except Exception as e:\n", "    print(f\"Error during testing set evaluation: {e}\")\n", "    print(\"Proceeding to final training and validation forecasting, but review test set evaluation.\")\n", "    test_forecast = pd.Series([]) # Indicate failure for plotting\n", "\n", "# --- 5. Final Model Training (on Training + Testing Sets) ---\n", "print(\"\\n--- 5. Training Final SARIMAX Model on Training + Testing Data ---\")\n", "\n", "# Combine Training and Testing sets for the final model training.\n", "# This maximizes the data available for the model before making final predictions on the validation set.\n", "full_train_df = pd.concat([train_df, test_df])\n", "y_full_train = full_train_df[target_variable]\n", "exog_full_train = full_train_df.drop(columns=[target_variable])\n", "\n", "# Re-train the SARIMAX model using the optimal orders found by auto_arima\n", "# on the combined training and testing data.\n", "try:\n", "    final_model = SARIMAX(y=y_full_train,\n", "                          exog=exog_full_train,\n", "                          order=smodel.order,\n", "                          seasonal_order=smodel.seasonal_order,\n", "                          enforce_stationarity=False, # Set to False to allow model to handle differencing implicitly\n", "                          enforce_invertibility=False) # Set to False for robustness\n", "    final_model_fit = final_model.fit(disp=False) # disp=False suppresses convergence messages\n", "    print(\"Final SARIMAX model trained successfully on combined Training + Testing data.\")\n", "    print(\"Final model summary (brief):\")\n", "    print(final_model_fit.summary().tables[0]) # Print only the main model info table\n", "except Exception as e:\n", "    print(f\"Error during final model training: {e}\")\n", "    print(\"Cannot proceed with validation forecasting.\")\n", "    exit()\n", "\n", "# --- 6. Forecast and Evaluate on Validation Set (24h and 168h) ---\n", "print(\"\\n--- 6. Forecasting and Evaluation on Validation Set ---\")\n", "\n", "# Get actual values for the required horizons from the validation set\n", "y_validation_24h_actual = y_validation.iloc[:24]\n", "y_validation_168h_actual = y_validation.iloc[:168]\n", "\n", "# Get corresponding exogenous variables for the forecast horizons from the validation set\n", "exog_validation_24h = exog_validation.iloc[:24]\n", "exog_validation_168h = exog_validation.iloc[:168]\n", "\n", "# Ensure exogenous data for validation set is aligned\n", "if not exog_validation_24h.index.equals(y_validation_24h_actual.index):\n", "    exog_validation_24h = exog_validation_24h.reindex(y_validation_24h_actual.index)\n", "if not exog_validation_168h.index.equals(y_validation_168h_actual.index):\n", "    exog_validation_168h = exog_validation_168h.reindex(y_validation_168h_actual.index)\n", "\n", "\n", "# Forecast 24 hours (1 day) on the validation set\n", "try:\n", "    forecast_24h = final_model_fit.predict(start=y_validation_24h_actual.index[0],\n", "                                           end=y_validation_24h_actual.index[-1],\n", "                                           exog=exog_validation_24h)\n", "    forecast_24h.index = y_validation_24h_actual.index # Align index for metrics\n", "\n", "    rmse_24h = np.sqrt(mean_squared_error(y_validation_24h_actual, forecast_24h))\n", "    mae_24h = mean_absolute_error(y_validation_24h_actual, forecast_24h)\n", "    mape_24h = np.mean(np.abs((y_validation_24h_actual - forecast_24h) / (y_validation_24h_actual + 1e-8))) * 100\n", "\n", "    print(f\"\\n--- Performance for 24-hour Forecast on Validation Set ---\")\n", "    print(f\"RMSE: {rmse_24h:.2f}\")\n", "    print(f\"MAE: {mae_24h:.2f}\")\n", "    print(f\"MAPE: {mape_24h:.2f}%\")\n", "except Exception as e:\n", "    print(f\"Error forecasting 24 hours: {e}\")\n", "    forecast_24h = pd.Series([], dtype=float) # Empty series if error\n", "    rmse_24h, mae_24h, mape_24h = np.nan, np.nan, np.nan\n", "\n", "# Forecast 168 hours (7 days) on the validation set\n", "try:\n", "    # Ensure n_periods is 168 if exog is used, or pass start/end\n", "    forecast_168h = final_model_fit.predict(start=y_validation_168h_actual.index[0],\n", "                                            end=y_validation_168h_actual.index[-1],\n", "                                            exog=exog_validation_168h)\n", "    forecast_168h.index = y_validation_168h_actual.index # Align index for metrics\n", "\n", "    rmse_168h = np.sqrt(mean_squared_error(y_validation_168h_actual, forecast_168h))\n", "    mae_168h = mean_absolute_error(y_validation_168h_actual, forecast_168h)\n", "    mape_168h = np.mean(np.abs((y_validation_168h_actual - forecast_168h) / (y_validation_168h_actual + 1e-8))) * 100\n", "\n", "    print(f\"\\n--- Performance for 168-hour Forecast on Validation Set ---\")\n", "    print(f\"RMSE: {rmse_168h:.2f}\")\n", "    print(f\"MAE: {mae_168h:.2f}\")\n", "    print(f\"MAPE: {mape_168h:.2f}%\")\n", "except Exception as e:\n", "    print(f\"Error forecasting 168 hours: {e}\")\n", "    forecast_168h = pd.Series([], dtype=float) # Empty series if error\n", "    rmse_168h, mae_168h, mape_168h = np.nan, np.nan, np.nan\n", "\n", "\n", "# --- 7. Visualization of Validation Forecasts ---\n", "print(\"\\n--- 7. Visualizing Forecasts on Validation Set ---\")\n", "plt.figure(figsize=(20, 8))\n", "\n", "# Plot the last part of the training + testing data for context\n", "plt.plot(y_full_train.index[-24*14:], y_full_train[-24*14:], label='Last 2 Weeks of Training + Testing Data', color='blue', alpha=0.7)\n", "# Plot the actual validation data\n", "plt.plot(y_validation.index, y_validation, label='Actual Validation Data', color='green')\n", "\n", "# Plot the 24-hour forecast if it was successful\n", "if not forecast_24h.empty:\n", "    plt.plot(forecast_24h.index, forecast_24h, label='SARIMAX 24h Forecast', color='red', linestyle='--')\n", "# Plot the 168-hour forecast if it was successful\n", "if not forecast_168h.empty:\n", "    plt.plot(forecast_168h.index, forecast_168h, label='SARIMAX 168h Forecast', color='purple', linestyle='-.')\n", "\n", "plt.title('Electric Demand Forecast on Validation Set (SARIMAX)')\n", "plt.xlabel('Date-Time')\n", "plt.ylabel('Demand')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n--- Program Execution Summary ---\")\n", "print(f\"SARIMAX Model with Exogenous Variables (Temperature, One-Hot Encoded WeekDay & Holiday)\")\n", "print(f\"Optimal orders (p,d,q)(P,D,Q,s): {smodel.order}{smodel.seasonal_order}\")\n", "print(f\"Test Set Performance: RMSE={rmse_test:.2f}, MAE={mae_test:.2f}, MAPE={mape_test:.2f}%\")\n", "print(f\"Validation (24h) Performance: RMSE={rmse_24h:.2f}, MAE={mae_24h:.2f}, MAPE={mape_24h:.2f}%\")\n", "print(f\"Validation (168h) Performance: RMSE={rmse_168h:.2f}, MAE={mae_168h:.2f}, MAPE={mape_168h:.2f}%\")\n", "print(\"\\nForecasts generated for 24-hour and 168-hour horizons on the validation set.\")\n", "print(\"The plots show the actual demand and the model's forecasts for these periods.\")"], "outputs": [], "execution_count": null, "metadata": {"id": "1-pgMkyF_JCJ"}}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}