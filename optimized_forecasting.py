#!/usr/bin/env python3
"""
Optimized Electric Demand Forecasting
ARIMA & Holt-Winter with Actual vs Predicted Comparisons
Concise implementation focused on results
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
import warnings
warnings.filterwarnings("ignore")

def metrics(actual, pred):
    """Calculate RMSE, MAE, MAPE"""
    rmse = np.sqrt(mean_squared_error(actual, pred))
    mae = mean_absolute_error(actual, pred)
    mape = np.mean(np.abs((actual - pred) / (actual + 1e-8))) * 100
    return rmse, mae, mape

def main():
    print("Electric Demand Forecasting: ARIMA vs Holt-Winter")
    print("="*50)
    
    # Load data
    df = pd.read_csv('electric_demand_1h.csv')
    df['datetime'] = pd.to_datetime(df[['Year', 'Month', 'Day', 'Hour']])
    df.set_index('datetime', inplace=True)
    demand = df['Demand']
    
    # Split data (80% train, 20% test)
    split = int(0.8 * len(demand))
    train, test = demand[:split], demand[split:]
    print(f"Data: {len(demand)} samples | Train: {len(train)} | Test: {len(test)}")
    
    results = {}
    
    # Train ARIMA
    print("\nTraining ARIMA...")
    try:
        arima = SARIMAX(train, order=(1,1,1), seasonal_order=(1,1,1,24)).fit(disp=False, maxiter=50)
        arima_train_pred = arima.fittedvalues
        arima_test_pred = arima.forecast(len(test))
        arima_test_pred.index = test.index
        
        results['ARIMA'] = {
            'train_pred': arima_train_pred,
            'test_pred': arima_test_pred,
            'train_metrics': metrics(train, arima_train_pred),
            'test_metrics': metrics(test, arima_test_pred)
        }
        print(f"✓ ARIMA - Test RMSE: {results['ARIMA']['test_metrics'][0]:.2f}")
    except Exception as e:
        print(f"✗ ARIMA failed: {e}")
    
    # Train Holt-Winter
    print("Training Holt-Winter...")
    try:
        hw = ExponentialSmoothing(train, trend='add', seasonal='mul', seasonal_periods=24).fit()
        hw_train_pred = hw.fittedvalues
        hw_test_pred = hw.forecast(len(test))
        hw_test_pred.index = test.index
        
        results['Holt-Winter'] = {
            'train_pred': hw_train_pred,
            'test_pred': hw_test_pred,
            'train_metrics': metrics(train, hw_train_pred),
            'test_metrics': metrics(test, hw_test_pred)
        }
        print(f"✓ Holt-Winter - Test RMSE: {results['Holt-Winter']['test_metrics'][0]:.2f}")
    except Exception as e:
        print(f"✗ Holt-Winter failed: {e}")
    
    # Create comparisons
    if results:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        colors = {'ARIMA': 'red', 'Holt-Winter': 'orange'}
        
        for i, (model, data) in enumerate(results.items()):
            # Training comparison (last 7 days)
            week_train = train[-168:]
            week_pred = data['train_pred'][-168:]
            
            axes[0, i].plot(week_train.index, week_train, 'b-', label='Actual', linewidth=2)
            axes[0, i].plot(week_pred.index, week_pred, '--', color=colors[model], 
                           label=f'{model} Forecast', linewidth=2)
            axes[0, i].set_title(f'{model}: Training (Last 7 Days)')
            axes[0, i].legend()
            axes[0, i].grid(True, alpha=0.3)
            
            # Test comparison (first 7 days)
            week_test = test[:168]
            week_test_pred = data['test_pred'][:168]
            
            axes[1, i].plot(week_test.index, week_test, 'g-', label='Actual', 
                           linewidth=2, marker='o', markersize=2)
            axes[1, i].plot(week_test_pred.index, week_test_pred, '--', color=colors[model],
                           label=f'{model} Forecast', linewidth=2, marker='s', markersize=2)
            axes[1, i].set_title(f'{model}: Test (First 7 Days)')
            axes[1, i].legend()
            axes[1, i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('forecast_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("\n✓ Comparison plots saved as 'forecast_comparison.png'")
        
        # Performance summary
        print("\nPerformance Summary:")
        print("-" * 60)
        print(f"{'Model':<12} {'Dataset':<8} {'RMSE':<10} {'MAE':<10} {'MAPE%':<8}")
        print("-" * 60)
        
        for model, data in results.items():
            train_rmse, train_mae, train_mape = data['train_metrics']
            test_rmse, test_mae, test_mape = data['test_metrics']
            print(f"{model:<12} {'Train':<8} {train_rmse:<10.2f} {train_mae:<10.2f} {train_mape:<8.2f}")
            print(f"{model:<12} {'Test':<8} {test_rmse:<10.2f} {test_mae:<10.2f} {test_mape:<8.2f}")
        
        # Best model
        best = min(results.items(), key=lambda x: x[1]['test_metrics'][0])
        print(f"\n🏆 Best Model: {best[0]} (Test RMSE: {best[1]['test_metrics'][0]:.2f})")
        
        print("\n✅ Forecasting completed successfully!")
        print("✅ Actual vs Predicted comparisons generated")
        print("✅ Performance metrics calculated")

if __name__ == "__main__":
    main()
