#!/usr/bin/env python3
"""
Simple Electric Demand Forecasting with Actual vs Predicted Comparisons
Focus: Get ARIMA and Holt-Winter working with clear comparisons
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
import warnings
warnings.filterwarnings("ignore")

# Configure matplotlib for better display
plt.rcParams['figure.figsize'] = [12, 8]
plt.rcParams['font.size'] = 10

def calculate_metrics(actual, predicted):
    """Calculate RMSE, MAE, MAPE"""
    rmse = np.sqrt(mean_squared_error(actual, predicted))
    mae = mean_absolute_error(actual, predicted)
    mape = np.mean(np.abs((actual - predicted) / (actual + 1e-8))) * 100
    return rmse, mae, mape

def main():
    print("="*60)
    print("ELECTRIC DEMAND FORECASTING")
    print("ARIMA & Holt-Winter with Actual vs Predicted")
    print("="*60)
    
    # 1. Load Data
    print("\n1. Loading data...")
    try:
        df = pd.read_csv('electric_demand_1h.csv')
        print(f"✓ Data loaded: {df.shape}")
        
        # Create datetime index
        df['datetime'] = pd.to_datetime(df[['Year', 'Month', 'Day', 'Hour']])
        df.set_index('datetime', inplace=True)
        df.index.freq = 'H'
        
        # Get demand series
        demand = df['Demand'].copy()
        print(f"✓ Demand series: {len(demand)} hourly observations")
        
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return
    
    # 2. Split Data
    print("\n2. Splitting data...")
    train_size = int(0.8 * len(demand))  # 80% for training
    test_size = len(demand) - train_size  # 20% for testing
    
    demand_train = demand.iloc[:train_size]
    demand_test = demand.iloc[train_size:]
    
    print(f"✓ Training: {len(demand_train)} samples")
    print(f"✓ Testing: {len(demand_test)} samples")
    
    # 3. Train ARIMA Model
    print("\n3. Training ARIMA model...")
    try:
        # Simple ARIMA model
        arima_model = SARIMAX(
            demand_train,
            order=(1, 1, 1),  # Simple ARIMA(1,1,1)
            seasonal_order=(1, 1, 1, 24),  # Daily seasonality
            enforce_stationarity=False,
            enforce_invertibility=False
        )
        
        arima_fit = arima_model.fit(disp=False, maxiter=50)
        print(f"✓ ARIMA model trained (AIC: {arima_fit.aic:.2f})")
        
        # Generate forecasts
        arima_train_pred = arima_fit.fittedvalues
        arima_test_pred = arima_fit.forecast(steps=len(demand_test))
        arima_test_pred.index = demand_test.index
        
        # Calculate metrics
        arima_train_rmse, arima_train_mae, arima_train_mape = calculate_metrics(demand_train, arima_train_pred)
        arima_test_rmse, arima_test_mae, arima_test_mape = calculate_metrics(demand_test, arima_test_pred)
        
        print(f"  Training RMSE: {arima_train_rmse:.2f}, MAPE: {arima_train_mape:.2f}%")
        print(f"  Test RMSE: {arima_test_rmse:.2f}, MAPE: {arima_test_mape:.2f}%")
        
        arima_success = True
        
    except Exception as e:
        print(f"✗ ARIMA training failed: {e}")
        arima_success = False
    
    # 4. Train Holt-Winter Model
    print("\n4. Training Holt-Winter model...")
    try:
        # Holt-Winter model
        hw_model = ExponentialSmoothing(
            demand_train,
            trend='add',
            seasonal='mul',
            seasonal_periods=24,
            damped_trend=True
        )
        
        hw_fit = hw_model.fit(optimized=True)
        print(f"✓ Holt-Winter model trained (AIC: {hw_fit.aic:.2f})")
        
        # Generate forecasts
        hw_train_pred = hw_fit.fittedvalues
        hw_test_pred = hw_fit.forecast(steps=len(demand_test))
        hw_test_pred.index = demand_test.index
        
        # Calculate metrics
        hw_train_rmse, hw_train_mae, hw_train_mape = calculate_metrics(demand_train, hw_train_pred)
        hw_test_rmse, hw_test_mae, hw_test_mape = calculate_metrics(demand_test, hw_test_pred)
        
        print(f"  Training RMSE: {hw_train_rmse:.2f}, MAPE: {hw_train_mape:.2f}%")
        print(f"  Test RMSE: {hw_test_rmse:.2f}, MAPE: {hw_test_mape:.2f}%")
        
        hw_success = True
        
    except Exception as e:
        print(f"✗ Holt-Winter training failed: {e}")
        hw_success = False
    
    # 5. Create Actual vs Predicted Comparisons
    print("\n5. Creating actual vs predicted comparisons...")
    
    if arima_success and hw_success:
        # Create comparison plots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # ARIMA Training Comparison (last 7 days)
        last_week_idx = -24*7
        axes[0, 0].plot(demand_train.iloc[last_week_idx:].index, 
                       demand_train.iloc[last_week_idx:], 
                       label='Actual', color='blue', linewidth=2)
        axes[0, 0].plot(arima_train_pred.iloc[last_week_idx:].index, 
                       arima_train_pred.iloc[last_week_idx:], 
                       label='ARIMA Forecast', color='red', linestyle='--', linewidth=2)
        axes[0, 0].set_title('ARIMA: Training Set (Last 7 Days)')
        axes[0, 0].set_ylabel('Demand')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Holt-Winter Training Comparison (last 7 days)
        axes[0, 1].plot(demand_train.iloc[last_week_idx:].index, 
                       demand_train.iloc[last_week_idx:], 
                       label='Actual', color='blue', linewidth=2)
        axes[0, 1].plot(hw_train_pred.iloc[last_week_idx:].index, 
                       hw_train_pred.iloc[last_week_idx:], 
                       label='Holt-Winter Forecast', color='orange', linestyle='--', linewidth=2)
        axes[0, 1].set_title('Holt-Winter: Training Set (Last 7 Days)')
        axes[0, 1].set_ylabel('Demand')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # ARIMA Test Comparison (first 7 days)
        first_week_test = demand_test.iloc[:24*7]
        first_week_arima = arima_test_pred.iloc[:24*7]
        
        axes[1, 0].plot(first_week_test.index, first_week_test, 
                       label='Actual', color='green', linewidth=2, marker='o', markersize=3)
        axes[1, 0].plot(first_week_arima.index, first_week_arima, 
                       label='ARIMA Forecast', color='red', linestyle='--', linewidth=2, marker='s', markersize=3)
        axes[1, 0].set_title('ARIMA: Test Set (First 7 Days)')
        axes[1, 0].set_xlabel('Date-Time')
        axes[1, 0].set_ylabel('Demand')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # Holt-Winter Test Comparison (first 7 days)
        first_week_hw = hw_test_pred.iloc[:24*7]
        
        axes[1, 1].plot(first_week_test.index, first_week_test, 
                       label='Actual', color='green', linewidth=2, marker='o', markersize=3)
        axes[1, 1].plot(first_week_hw.index, first_week_hw, 
                       label='Holt-Winter Forecast', color='orange', linestyle='--', linewidth=2, marker='^', markersize=3)
        axes[1, 1].set_title('Holt-Winter: Test Set (First 7 Days)')
        axes[1, 1].set_xlabel('Date-Time')
        axes[1, 1].set_ylabel('Demand')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('actual_vs_predicted_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()  # Close the plot instead of showing it
        
        print("✓ Comparison plots created and saved as 'actual_vs_predicted_comparison.png'")
        
        # Performance Summary
        print("\n6. Performance Summary:")
        print("="*60)
        print(f"{'Model':<15} {'Dataset':<10} {'RMSE':<10} {'MAE':<10} {'MAPE (%)':<10}")
        print("="*60)
        print(f"{'ARIMA':<15} {'Training':<10} {arima_train_rmse:<10.2f} {arima_train_mae:<10.2f} {arima_train_mape:<10.2f}")
        print(f"{'ARIMA':<15} {'Test':<10} {arima_test_rmse:<10.2f} {arima_test_mae:<10.2f} {arima_test_mape:<10.2f}")
        print(f"{'Holt-Winter':<15} {'Training':<10} {hw_train_rmse:<10.2f} {hw_train_mae:<10.2f} {hw_train_mape:<10.2f}")
        print(f"{'Holt-Winter':<15} {'Test':<10} {hw_test_rmse:<10.2f} {hw_test_mae:<10.2f} {hw_test_mape:<10.2f}")
        print("="*60)
        
        # Determine best model
        if arima_test_rmse < hw_test_rmse:
            print(f"🏆 Best Model: ARIMA (Test RMSE: {arima_test_rmse:.2f})")
        else:
            print(f"🏆 Best Model: Holt-Winter (Test RMSE: {hw_test_rmse:.2f})")
        
    else:
        print("⚠ Some models failed. Cannot create full comparison.")
    
    print("\n" + "="*60)
    print("✅ FORECASTING COMPLETED!")
    print("✅ Both ARIMA and Holt-Winter models trained")
    print("✅ Actual vs Predicted comparisons generated")
    print("✅ Performance metrics calculated")
    print("✅ Visualization saved as PNG file")
    print("="*60)

if __name__ == "__main__":
    main()
