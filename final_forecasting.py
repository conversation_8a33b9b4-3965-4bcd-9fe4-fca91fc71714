#!/usr/bin/env python3
"""
Electric Demand Forecasting - Final Optimized Version
ARIMA & Holt-Winter with Validation Set Forecasting
Following 4-Phase Structure: Train → Test → Validation → Compare
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
import warnings
warnings.filterwarnings("ignore")

def metrics(actual, pred):
    """Calculate RMSE, MAE, MAPE"""
    rmse = np.sqrt(mean_squared_error(actual, pred))
    mae = mean_absolute_error(actual, pred)
    mape = np.mean(np.abs((actual - pred) / (actual + 1e-8))) * 100
    return rmse, mae, mape

def train_model(train_data, model_type):
    """Train ARIMA or Holt-Winter model"""
    if model_type == 'ARIMA':
        model = SARIMAX(train_data, order=(1,1,1), seasonal_order=(1,1,1,24))
        return model.fit(disp=False, maxiter=50)
    else:  # Holt-Winter
        model = ExponentialSmoothing(train_data, trend='add', seasonal='mul', seasonal_periods=24)
        return model.fit(optimized=True)

def forecast_model(model, steps, model_type):
    """Generate forecasts"""
    if model_type == 'ARIMA':
        return model.forecast(steps)
    else:  # Holt-Winter
        return model.forecast(steps)

def main():
    print("Electric Demand Forecasting - 4 Phase Implementation")
    print("Phase 1: Data Prep | Phase 2: Train | Phase 3: Test | Phase 4: Validation")
    print("="*70)
    
    # Phase 1: Data Preparation
    df = pd.read_csv('electric_demand_1h.csv')
    df['datetime'] = pd.to_datetime(df[['Year', 'Month', 'Day', 'Hour']])
    df.set_index('datetime', inplace=True)
    demand = df['Demand']
    
    # Split: 70% train, 20% test, 10% validation (as per guide)
    total = len(demand)
    train_size = int(0.7 * total)
    test_size = int(0.2 * total)
    
    train = demand[:train_size]
    test = demand[train_size:train_size + test_size]
    validation = demand[train_size + test_size:]
    
    print(f"Data Split: Train={len(train)} | Test={len(test)} | Validation={len(validation)}")
    
    models = {}
    results = {}
    
    # Phase 2: Model Training
    print("\nPhase 2: Training Models...")
    for model_name in ['ARIMA', 'Holt-Winter']:
        try:
            model = train_model(train, model_name)
            models[model_name] = model
            print(f"✓ {model_name} trained successfully")
        except Exception as e:
            print(f"✗ {model_name} failed: {e}")
    
    # Phase 3: Test Set Forecasting
    print("\nPhase 3: Test Set Forecasting...")
    for model_name, model in models.items():
        test_pred = forecast_model(model, len(test), model_name)
        test_pred.index = test.index
        test_rmse, test_mae, test_mape = metrics(test, test_pred)
        
        results[model_name] = {
            'test_pred': test_pred,
            'test_metrics': (test_rmse, test_mae, test_mape)
        }
        print(f"✓ {model_name} Test RMSE: {test_rmse:.2f}, MAPE: {test_mape:.2f}%")
    
    # Phase 4: Validation Set Forecasting (Final Evaluation)
    print("\nPhase 4: Validation Set Forecasting...")
    print("Training final models on combined train+test data...")
    
    # Combine train and test for final model training
    combined_train = pd.concat([train, test])
    
    for model_name in models.keys():
        try:
            # Retrain on combined data
            final_model = train_model(combined_train, model_name)
            
            # Forecast validation set
            val_pred = forecast_model(final_model, len(validation), model_name)
            val_pred.index = validation.index
            val_rmse, val_mae, val_mape = metrics(validation, val_pred)
            
            results[model_name]['val_pred'] = val_pred
            results[model_name]['val_metrics'] = (val_rmse, val_mae, val_mape)
            
            print(f"✓ {model_name} Validation RMSE: {val_rmse:.2f}, MAPE: {val_mape:.2f}%")
            
        except Exception as e:
            print(f"✗ {model_name} validation failed: {e}")
    
    # Create Actual vs Predicted Comparisons
    print("\nCreating Actual vs Predicted Comparisons...")
    
    if len(results) >= 2:
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        colors = {'ARIMA': 'red', 'Holt-Winter': 'orange'}
        
        for i, (model_name, data) in enumerate(results.items()):
            if 'test_pred' in data and 'val_pred' in data:
                
                # Test Set Comparison (first 7 days)
                test_week = test[:168]
                test_pred_week = data['test_pred'][:168]
                
                axes[i, 0].plot(test_week.index, test_week, 'g-', label='Actual', linewidth=2)
                axes[i, 0].plot(test_pred_week.index, test_pred_week, '--', 
                               color=colors[model_name], label='Forecast', linewidth=2)
                axes[i, 0].set_title(f'{model_name}: Test Set (7 Days)')
                axes[i, 0].legend()
                axes[i, 0].grid(True, alpha=0.3)
                
                # Validation Set Comparison (first 7 days)
                val_week = validation[:168] if len(validation) >= 168 else validation
                val_pred_week = data['val_pred'][:len(val_week)]
                
                axes[i, 1].plot(val_week.index, val_week, 'b-', label='Actual', 
                               linewidth=2, marker='o', markersize=2)
                axes[i, 1].plot(val_pred_week.index, val_pred_week, '--', 
                               color=colors[model_name], label='Forecast', 
                               linewidth=2, marker='s', markersize=2)
                axes[i, 1].set_title(f'{model_name}: Validation Set (7 Days)')
                axes[i, 1].legend()
                axes[i, 1].grid(True, alpha=0.3)
                
                # Full Validation Set
                axes[i, 2].plot(validation.index, validation, 'b-', label='Actual', linewidth=1.5)
                axes[i, 2].plot(data['val_pred'].index, data['val_pred'], '--', 
                               color=colors[model_name], label='Forecast', linewidth=1.5)
                axes[i, 2].set_title(f'{model_name}: Full Validation Period')
                axes[i, 2].legend()
                axes[i, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('validation_forecast_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Validation comparisons saved as 'validation_forecast_comparison.png'")
    
    # Final Performance Summary
    print("\n" + "="*70)
    print("FINAL PERFORMANCE SUMMARY")
    print("="*70)
    print(f"{'Model':<12} {'Phase':<12} {'RMSE':<10} {'MAE':<10} {'MAPE%':<8}")
    print("-" * 70)
    
    for model_name, data in results.items():
        if 'test_metrics' in data:
            test_rmse, test_mae, test_mape = data['test_metrics']
            print(f"{model_name:<12} {'Test':<12} {test_rmse:<10.2f} {test_mae:<10.2f} {test_mape:<8.2f}")
        
        if 'val_metrics' in data:
            val_rmse, val_mae, val_mape = data['val_metrics']
            print(f"{model_name:<12} {'Validation':<12} {val_rmse:<10.2f} {val_mae:<10.2f} {val_mape:<8.2f}")
    
    # Best Model Selection
    if results:
        best_model = min(results.items(), 
                        key=lambda x: x[1]['val_metrics'][0] if 'val_metrics' in x[1] else float('inf'))
        
        if 'val_metrics' in best_model[1]:
            best_rmse = best_model[1]['val_metrics'][0]
            print(f"\n🏆 BEST MODEL: {best_model[0]} (Validation RMSE: {best_rmse:.2f})")
        
        print("\n✅ 4-PHASE FORECASTING COMPLETED!")
        print("✅ Phase 1: Data prepared with 70/20/10 split")
        print("✅ Phase 2: ARIMA & Holt-Winter models trained")
        print("✅ Phase 3: Test set forecasting completed")
        print("✅ Phase 4: Validation set forecasting completed")
        print("✅ Actual vs Predicted comparisons for validation set generated")
        print("="*70)

if __name__ == "__main__":
    main()
