# Import all required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from statsmodels.stats.diagnostic import acorr_ljungbox
from scipy import stats
import warnings
warnings.filterwarnings("ignore")

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")
%matplotlib inline

print("✅ All libraries imported successfully!")
print("📊 Ready for electric demand forecasting analysis")

def print_phase_header(title):
    """Print formatted phase header"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80)

def calculate_metrics(actual, predicted):
    """Calculate RMSE, MAE, MAPE"""
    rmse = np.sqrt(mean_squared_error(actual, predicted))
    mae = mean_absolute_error(actual, predicted)
    mape = np.mean(np.abs((actual - predicted) / (actual + 1e-8))) * 100
    return rmse, mae, mape

def test_stationarity(timeseries, title="Time Series"):
    """Perform ADF test for stationarity"""
    print(f"\nStationarity Test for {title}:")
    print("-" * 50)
    
    result = adfuller(timeseries.dropna())
    print(f"ADF Statistic: {result[0]:.6f}")
    print(f"p-value: {result[1]:.6f}")
    print("Critical Values:")
    for key, value in result[4].items():
        print(f"\t{key}: {value:.3f}")
    
    if result[1] <= 0.05:
        print("✓ Series is stationary (reject null hypothesis)")
        return True
    else:
        print("✗ Series is non-stationary (fail to reject null hypothesis)")
        return False

print("✅ Helper functions defined successfully!")

print_phase_header("PHASE 1: DATA PREPARATION AND EXPLORATORY DATA ANALYSIS")

print("\n1. LOAD AND PARSE DATA")
print("-" * 30)

try:
    # Load dataset
    df = pd.read_csv('electric_demand_1h.csv')
    print(f"✓ Dataset loaded: {df.shape}")
    print(f"  Columns: {list(df.columns)}")
    
    # Display first few rows
    print("\n📊 First 5 rows of the dataset:")
    display(df.head())
    
    # Create proper datetime index
    print("\n✓ Creating proper datetime index...")
    df['datetime'] = pd.to_datetime(df[['Year', 'Month', 'Day', 'Hour']])
    df.set_index('datetime', inplace=True)
    df.index.freq = 'H'  # Set hourly frequency for statsmodels
    
    print(f"  Date range: {df.index[0]} to {df.index[-1]}")
    print(f"  Frequency set to: {df.index.freq}")
    
    # Verify no missing values
    missing = df.isnull().sum()
    print(f"\n✓ Missing values check:")
    print(missing)
    if missing.any():
        print("⚠ Warning: Missing values detected!")
    else:
        print("✓ Confirmed: No missing values")
        
except Exception as e:
    print(f"✗ Error loading data: {e}")
    raise

print("\n2. HANDLE EXOGENOUS VARIABLES")
print("-" * 35)

print("✓ Identified exogenous variables:")
print("  - WeekDay (categorical)")
print("  - Holiday (categorical)")  
print("  - Temperature (numerical)")

# Categorical encoding
print("\n✓ Applying One-Hot Encoding for categorical variables...")

# WeekDay dummies (drop first to avoid multicollinearity)
weekday_dummies = pd.get_dummies(df['WeekDay'], prefix='WeekDay', drop_first=True)
print(f"  WeekDay dummies: {list(weekday_dummies.columns)}")

# Holiday dummy (drop first to avoid multicollinearity)
holiday_dummy = pd.get_dummies(df['Holiday'], prefix='Holiday', drop_first=True)
print(f"  Holiday dummy: {list(holiday_dummy.columns)}")

# Combine exogenous variables
exog_vars = ['Temperature'] + list(weekday_dummies.columns) + list(holiday_dummy.columns)
exog_df = pd.concat([df[['Temperature']], weekday_dummies, holiday_dummy], axis=1)

print(f"\n✓ Final exogenous variables ({len(exog_vars)}):")
for i, var in enumerate(exog_vars, 1):
    print(f"  {i}. {var}")

# Create processed dataframe
df_processed = pd.concat([df[['Demand']], exog_df], axis=1)
df_processed.columns = ['demand'] + exog_vars

print(f"\n📊 Processed dataset shape: {df_processed.shape}")
display(df_processed.head())

print("\n3. EXPLORATORY DATA ANALYSIS (EDA)")
print("-" * 40)

demand = df_processed['demand']

# Basic statistics
print("✓ Basic Statistics:")
print(demand.describe())

# Visualize demand over time
print("\n✓ Visualizing demand over time...")
fig, axes = plt.subplots(3, 1, figsize=(20, 12))

# Full time series
axes[0].plot(demand.index, demand, alpha=0.7, linewidth=0.5)
axes[0].set_title('Electric Demand Time Series - Full Dataset\n(Overall trends and seasonal patterns)', fontsize=14)
axes[0].set_ylabel('Demand')
axes[0].grid(True, alpha=0.3)

# One month detail
one_month = demand['2013-06-01':'2013-06-30']
axes[1].plot(one_month.index, one_month, color='blue', linewidth=1)
axes[1].set_title('Electric Demand - June 2013 (One Month Detail)\n(Daily and weekly patterns)', fontsize=14)
axes[1].set_ylabel('Demand')
axes[1].grid(True, alpha=0.3)

# One week detail
one_week = demand['2013-06-01':'2013-06-07']
axes[2].plot(one_week.index, one_week, color='green', linewidth=2, marker='o', markersize=3)
axes[2].set_title('Electric Demand - First Week June 2013\n(Hourly patterns and outliers)', fontsize=14)
axes[2].set_xlabel('Date-Time')
axes[2].set_ylabel('Demand')
axes[2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Visualize relationships with exogenous variables
print("✓ Visualizing relationships with exogenous variables...")
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# Demand vs Temperature
axes[0, 0].scatter(df_processed['Temperature'], demand, alpha=0.5, s=1)
correlation = df_processed['Temperature'].corr(demand)
axes[0, 0].set_xlabel('Temperature')
axes[0, 0].set_ylabel('Demand')
axes[0, 0].set_title(f'Demand vs Temperature\n(Correlation: {correlation:.3f})')
axes[0, 0].grid(True, alpha=0.3)

# Average demand by hour
hourly_avg = df.groupby('Hour')['Demand'].mean()
axes[0, 1].plot(hourly_avg.index, hourly_avg.values, marker='o', linewidth=2, markersize=6)
axes[0, 1].set_xlabel('Hour of Day')
axes[0, 1].set_ylabel('Average Demand')
axes[0, 1].set_title('Average Demand by Hour\n(Daily seasonality)')
axes[0, 1].grid(True, alpha=0.3)
axes[0, 1].set_xticks(range(0, 24, 2))

# Average demand by weekday
weekday_avg = df.groupby('WeekDay')['Demand'].mean()
weekday_labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
axes[0, 2].bar(range(1, 8), weekday_avg.values, color='skyblue', alpha=0.8)
axes[0, 2].set_xlabel('Day of Week')
axes[0, 2].set_ylabel('Average Demand')
axes[0, 2].set_title('Average Demand by WeekDay\n(Weekly seasonality)')
axes[0, 2].set_xticks(range(1, 8))
axes[0, 2].set_xticklabels(weekday_labels)
axes[0, 2].grid(True, alpha=0.3, axis='y')

# Holiday vs Non-Holiday
holiday_comparison = df.groupby('Holiday')['Demand'].mean()
axes[1, 0].bar(['Non-Holiday', 'Holiday'], holiday_comparison.values, 
            color=['lightcoral', 'lightgreen'], alpha=0.8)
axes[1, 0].set_ylabel('Average Demand')
axes[1, 0].set_title('Holiday vs Non-Holiday\n(Holiday effect)')
axes[1, 0].grid(True, alpha=0.3, axis='y')

# Add values on bars
for i, v in enumerate(holiday_comparison.values):
    axes[1, 0].text(i, v + 50, f'{v:.0f}', ha='center', va='bottom', fontweight='bold')

# Monthly patterns
monthly_avg = df.groupby('Month')['Demand'].mean()
month_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
               'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
axes[1, 1].plot(range(1, 13), monthly_avg.values, marker='o', linewidth=3, markersize=8)
axes[1, 1].set_xlabel('Month')
axes[1, 1].set_ylabel('Average Demand')
axes[1, 1].set_title('Average Demand by Month\n(Annual seasonality)')
axes[1, 1].set_xticks(range(1, 13))
axes[1, 1].set_xticklabels(month_labels, rotation=45)
axes[1, 1].grid(True, alpha=0.3)

# Yearly trend
yearly_avg = df.groupby('Year')['Demand'].mean()
axes[1, 2].plot(yearly_avg.index, yearly_avg.values, marker='s', linewidth=3, markersize=10)
axes[1, 2].set_xlabel('Year')
axes[1, 2].set_ylabel('Average Demand')
axes[1, 2].set_title('Average Demand by Year\n(Overall trend)')
axes[1, 2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()