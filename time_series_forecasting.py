#!/usr/bin/env python3
"""
Electric Demand Forecasting with ARIMA/SARIMA and Holt-Winter Methods

This script implements comprehensive time series forecasting for electric demand data using:
- ARIMA/SARIMA models with exogenous variables
- Holt-Winter (Triple Exponential Smoothing) methods
- Model comparison and evaluation

Dataset Information:
- File: electric_demand_1h.csv
- Frequency: Hourly data (1 hour/sample)
- Target Variable: Demand (electricity demand)
- Exogenous Variables: WeekDay, Holiday, Temperature
- Holiday Rule: Weekends (WeekDay 6-7) are counted as holidays
- Missing Data: None
- Outliers: Kept as they reflect real-world events

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error
# from pmdarima import auto_arima  # Optional - will use manual ARIMA if not available
try:
    from pmdarima import auto_arima
    PMDARIMA_AVAILABLE = True
except ImportError:
    print("⚠ pmdarima not available. Will use manual ARIMA parameter selection.")
    PMDARIMA_AVAILABLE = False
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from scipy import stats
import warnings
warnings.filterwarnings("ignore")

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def print_section_header(title):
    """Print formatted section header"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def calculate_metrics(actual, predicted):
    """Calculate evaluation metrics"""
    rmse = np.sqrt(mean_squared_error(actual, predicted))
    mae = mean_absolute_error(actual, predicted)
    mape = np.mean(np.abs((actual - predicted) / (actual + 1e-8))) * 100
    return rmse, mae, mape

def test_stationarity(timeseries, title="Time Series"):
    """Test stationarity using ADF and KPSS tests"""
    print(f"\nStationarity Tests for {title}:")
    print("-" * 40)
    
    # ADF Test
    adf_result = adfuller(timeseries.dropna())
    print(f"ADF Test:")
    print(f"  ADF Statistic: {adf_result[0]:.6f}")
    print(f"  p-value: {adf_result[1]:.6f}")
    print(f"  Critical Values:")
    for key, value in adf_result[4].items():
        print(f"    {key}: {value:.3f}")
    
    if adf_result[1] <= 0.05:
        print("  Result: Series is stationary (reject null hypothesis)")
    else:
        print("  Result: Series is non-stationary (fail to reject null hypothesis)")
    
    # KPSS Test
    kpss_result = kpss(timeseries.dropna())
    print(f"\nKPSS Test:")
    print(f"  KPSS Statistic: {kpss_result[0]:.6f}")
    print(f"  p-value: {kpss_result[1]:.6f}")
    print(f"  Critical Values:")
    for key, value in kpss_result[3].items():
        print(f"    {key}: {value:.3f}")
    
    if kpss_result[1] <= 0.05:
        print("  Result: Series is non-stationary (reject null hypothesis)")
    else:
        print("  Result: Series is stationary (fail to reject null hypothesis)")

def load_and_preprocess_data():
    """Load and preprocess the electric demand data"""
    print_section_header("1. DATA LOADING AND PREPROCESSING")
    
    try:
        df = pd.read_csv('electric_demand_1h.csv')
        print("✓ Dataset 'electric_demand_1h.csv' loaded successfully.")
        print(f"  Dataset shape: {df.shape}")
    except FileNotFoundError:
        print("✗ Error: 'electric_demand_1h.csv' not found.")
        print("  Please ensure the file is in the same directory as the script.")
        return None, None, None, None, None, None
    
    # Display basic info
    print(f"\nDataset Info:")
    print(f"  Columns: {list(df.columns)}")
    print(f"  Date range: {df['DateTime'].min()} to {df['DateTime'].max()}")
    
    # Create datetime index
    print("\n✓ Creating datetime index...")
    df['datetime'] = pd.to_datetime(df['DateTime'])
    df.set_index('datetime', inplace=True)
    df.sort_index(inplace=True)
    df.index.freq = 'H'  # Set hourly frequency
    
    # Check for missing values
    print("\n✓ Checking for missing values:")
    missing_values = df.isnull().sum()
    print(missing_values)
    if missing_values.any():
        print("⚠ Warning: Missing values detected despite user confirmation.")
    else:
        print("✓ No missing values found.")
    
    # Prepare target variable (case-insensitive)
    target_col = None
    for col in df.columns:
        if col.lower() == 'demand':
            target_col = col
            break
    
    if target_col is None:
        print("✗ Error: 'Demand' column not found in dataset.")
        return None, None, None, None, None, None
    
    print(f"\n✓ Target variable: {target_col}")
    
    # Prepare exogenous variables
    print("\n✓ Preparing exogenous variables...")
    
    # One-hot encode WeekDay (drop first to avoid multicollinearity)
    weekday_dummies = pd.get_dummies(df['WeekDay'], prefix='WeekDay', drop_first=True)
    
    # One-hot encode Holiday (drop first to avoid multicollinearity)
    holiday_dummy = pd.get_dummies(df['Holiday'], prefix='Holiday', drop_first=True)
    
    # Combine exogenous variables
    exog_vars_df = df[['Temperature']].copy()
    exog_vars_df = pd.concat([exog_vars_df, weekday_dummies, holiday_dummy], axis=1)
    
    print(f"  Exogenous variables: {list(exog_vars_df.columns)}")
    print(f"  Shape: {exog_vars_df.shape}")
    
    # Combine target and exogenous variables
    df_processed = pd.concat([df[target_col], exog_vars_df], axis=1)
    df_processed.columns = ['demand'] + list(exog_vars_df.columns)
    
    return df_processed, df, target_col, exog_vars_df, weekday_dummies, holiday_dummy

def split_data(df_processed):
    """Split data into train, test, and validation sets"""
    print_section_header("2. DATA SPLITTING")
    
    total_rows = len(df_processed)
    train_size = int(0.7 * total_rows)
    test_size = int(0.2 * total_rows)
    validation_size = total_rows - train_size - test_size
    
    train_df = df_processed.iloc[:train_size]
    test_df = df_processed.iloc[train_size:train_size + test_size]
    validation_df = df_processed.iloc[train_size + test_size:]
    
    print(f"✓ Data splitting completed:")
    print(f"  Total data points: {total_rows}")
    print(f"  Train set (70%): {len(train_df)} rows")
    print(f"  Test set (20%): {len(test_df)} rows")
    print(f"  Validation set (10%): {len(validation_df)} rows")
    
    # Define target and exogenous variables for each split
    y_train = train_df['demand']
    exog_train = train_df.drop(columns=['demand'])
    
    y_test = test_df['demand']
    exog_test = test_df.drop(columns=['demand'])
    
    y_validation = validation_df['demand']
    exog_validation = validation_df.drop(columns=['demand'])
    
    return (train_df, test_df, validation_df, 
            y_train, exog_train, y_test, exog_test, y_validation, exog_validation)

def exploratory_data_analysis(df_processed, y_train):
    """Perform exploratory data analysis"""
    print_section_header("3. EXPLORATORY DATA ANALYSIS")
    
    # Basic statistics
    print("✓ Basic Statistics:")
    print(df_processed['demand'].describe())
    
    # Time series plot
    plt.figure(figsize=(15, 10))
    
    # Full time series
    plt.subplot(3, 1, 1)
    plt.plot(df_processed.index, df_processed['demand'], alpha=0.7)
    plt.title('Electric Demand Time Series (Full Dataset)')
    plt.ylabel('Demand')
    plt.grid(True)
    
    # Training data only
    plt.subplot(3, 1, 2)
    plt.plot(y_train.index, y_train, color='blue', alpha=0.8)
    plt.title('Electric Demand Time Series (Training Data)')
    plt.ylabel('Demand')
    plt.grid(True)
    
    # Last month of training data for detail
    plt.subplot(3, 1, 3)
    last_month = y_train[-24*30:]  # Last 30 days
    plt.plot(last_month.index, last_month, color='green')
    plt.title('Electric Demand - Last 30 Days of Training Data')
    plt.xlabel('Date-Time')
    plt.ylabel('Demand')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    # Seasonal decomposition
    print("\n✓ Performing seasonal decomposition...")
    decomposition = seasonal_decompose(y_train, model='additive', period=24*7)  # Weekly seasonality
    
    plt.figure(figsize=(15, 12))
    
    plt.subplot(4, 1, 1)
    plt.plot(decomposition.observed)
    plt.title('Original Time Series')
    plt.ylabel('Demand')
    
    plt.subplot(4, 1, 2)
    plt.plot(decomposition.trend)
    plt.title('Trend Component')
    plt.ylabel('Trend')
    
    plt.subplot(4, 1, 3)
    plt.plot(decomposition.seasonal)
    plt.title('Seasonal Component')
    plt.ylabel('Seasonal')
    
    plt.subplot(4, 1, 4)
    plt.plot(decomposition.resid)
    plt.title('Residual Component')
    plt.xlabel('Date-Time')
    plt.ylabel('Residual')
    
    plt.tight_layout()
    plt.show()
    
    # Stationarity tests
    test_stationarity(y_train, "Training Data")
    
    # ACF and PACF plots
    print("\n✓ Generating ACF and PACF plots...")
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Original series
    plot_acf(y_train.dropna(), ax=axes[0, 0], lags=50, title='ACF - Original Series')
    plot_pacf(y_train.dropna(), ax=axes[0, 1], lags=50, title='PACF - Original Series')
    
    # First difference
    y_train_diff = y_train.diff().dropna()
    plot_acf(y_train_diff, ax=axes[1, 0], lags=50, title='ACF - First Difference')
    plot_pacf(y_train_diff, ax=axes[1, 1], lags=50, title='PACF - First Difference')
    
    plt.tight_layout()
    plt.show()
    
    # Test stationarity of differenced series
    test_stationarity(y_train_diff, "First Differenced Series")
    
    return decomposition

if __name__ == "__main__":
    print("="*60)
    print("ELECTRIC DEMAND FORECASTING PROGRAM")
    print("ARIMA/SARIMA & Holt-Winter Methods")
    print("="*60)
    
    # Load and preprocess data
    result = load_and_preprocess_data()
    if result[0] is None:
        exit(1)
    
    df_processed, df_original, target_col, exog_vars_df, weekday_dummies, holiday_dummy = result
    
    # Split data
    splits = split_data(df_processed)
    (train_df, test_df, validation_df, 
     y_train, exog_train, y_test, exog_test, y_validation, exog_validation) = splits
    
    # Exploratory data analysis
    decomposition = exploratory_data_analysis(df_processed, y_train)
    
def train_arima_sarima_model(y_train, exog_train, y_test, exog_test):
    """Train ARIMA/SARIMA model using auto_arima or manual selection"""
    print_section_header("4. ARIMA/SARIMA MODEL TRAINING")

    if PMDARIMA_AVAILABLE:
        print("✓ Using pmdarima for automatic parameter selection...")
        print("  Finding optimal SARIMAX parameters with exogenous variables...")
        print("  This may take some time...")
        print("  Considering daily seasonality (m=24) and weekly patterns")

        try:
            # First attempt with comprehensive search
            print("\n  Attempting comprehensive parameter search...")
            smodel = auto_arima(
                y=y_train,
                exogenous=exog_train,
                start_p=1, start_q=1,
                max_p=5, max_q=5,
                start_P=0, start_Q=0,
                max_P=2, max_Q=2,
                m=24,  # Daily seasonality (24 hours)
                d=None, D=1,  # Let auto_arima determine d, enforce seasonal differencing
                trace=True,
                error_action='ignore',
                suppress_warnings=True,
                stepwise=True,
                n_jobs=-1,
                seasonal=True
            )
            print("✓ Comprehensive search completed successfully!")

        except Exception as e:
            print(f"⚠ Comprehensive search failed: {e}")
            print("  Attempting constrained search for robustness...")

            try:
                smodel = auto_arima(
                    y=y_train,
                    exogenous=exog_train,
                    start_p=1, start_q=1,
                    max_p=3, max_q=3,
                    start_P=0, start_Q=0,
                    max_P=1, max_Q=1,
                    m=24,
                    d=None, D=1,
                    trace=True,
                    error_action='ignore',
                    suppress_warnings=True,
                    stepwise=True,
                    n_jobs=1,
                    seasonal=True
                )
                print("✓ Constrained search completed successfully!")

            except Exception as e2:
                print(f"✗ Both searches failed: {e2}")
                print("  Falling back to simple ARIMA model...")

                smodel = auto_arima(
                    y=y_train,
                    start_p=1, start_q=1,
                    max_p=3, max_q=3,
                    d=None,
                    trace=True,
                    error_action='ignore',
                    suppress_warnings=True,
                    stepwise=True,
                    seasonal=False
                )
                print("✓ Simple ARIMA model fitted successfully!")

        print(f"\n✓ Optimal Model Summary:")
        print(f"  Non-seasonal order (p,d,q): {smodel.order}")
        print(f"  Seasonal order (P,D,Q,s): {smodel.seasonal_order}")
        print(f"  AIC: {smodel.aic():.2f}")

    else:
        print("✓ Using manual SARIMAX parameter selection...")
        print("  Fitting SARIMAX model with reasonable default parameters...")

        # Manual SARIMAX with reasonable parameters for hourly data
        try:
            smodel = SARIMAX(
                y=y_train,
                exog=exog_train,
                order=(2, 1, 2),  # ARIMA(2,1,2)
                seasonal_order=(1, 1, 1, 24),  # Seasonal(1,1,1) with 24-hour period
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            smodel_fit = smodel.fit(disp=False)

            # Create a wrapper object to mimic pmdarima's auto_arima output
            class ManualARIMAWrapper:
                def __init__(self, fitted_model):
                    self.fitted_model = fitted_model
                    self.order = fitted_model.model.order
                    self.seasonal_order = fitted_model.model.seasonal_order

                def aic(self):
                    return self.fitted_model.aic

                def predict(self, n_periods, exogenous=None):
                    return self.fitted_model.forecast(steps=n_periods, exog=exogenous)

            smodel = ManualARIMAWrapper(smodel_fit)
            print("✓ Manual SARIMAX model fitted successfully!")

        except Exception as e:
            print(f"⚠ Manual SARIMAX failed: {e}")
            print("  Trying simpler ARIMA model without seasonality...")

            try:
                smodel = SARIMAX(
                    y=y_train,
                    exog=exog_train,
                    order=(2, 1, 2),  # ARIMA(2,1,2)
                    enforce_stationarity=False,
                    enforce_invertibility=False
                )
                smodel_fit = smodel.fit(disp=False)
                smodel = ManualARIMAWrapper(smodel_fit)
                print("✓ Simple ARIMA model fitted successfully!")

            except Exception as e2:
                print(f"✗ All ARIMA attempts failed: {e2}")
                return None, None, (np.nan, np.nan, np.nan)

        print(f"\n✓ Manual Model Summary:")
        print(f"  Non-seasonal order (p,d,q): {smodel.order}")
        print(f"  Seasonal order (P,D,Q,s): {smodel.seasonal_order}")
        print(f"  AIC: {smodel.aic():.2f}")

    # Evaluate on test set
    print("\n✓ Evaluating on test set...")
    try:
        test_forecast = smodel.predict(n_periods=len(y_test), exogenous=exog_test)
        test_forecast.index = y_test.index

        rmse_test, mae_test, mape_test = calculate_metrics(y_test, test_forecast)

        print(f"  Test Set Performance:")
        print(f"    RMSE: {rmse_test:.2f}")
        print(f"    MAE: {mae_test:.2f}")
        print(f"    MAPE: {mape_test:.2f}%")

        # Plot test forecast
        plt.figure(figsize=(15, 8))
        plt.plot(y_train.index[-24*7:], y_train[-24*7:],
                label='Last Week of Training', color='blue', alpha=0.7)
        plt.plot(y_test.index, y_test, label='Actual Test Data', color='green')
        plt.plot(test_forecast.index, test_forecast,
                label='SARIMAX Forecast', color='red', linestyle='--')
        plt.title('SARIMAX Model - Test Set Forecast vs Actual')
        plt.xlabel('Date-Time')
        plt.ylabel('Demand')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()

    except Exception as e:
        print(f"⚠ Test evaluation failed: {e}")
        test_forecast = pd.Series([], dtype=float)
        rmse_test, mae_test, mape_test = np.nan, np.nan, np.nan

    return smodel, test_forecast, (rmse_test, mae_test, mape_test)

def train_holt_winter_model(y_train, y_test):
    """Train Holt-Winter (Triple Exponential Smoothing) model"""
    print_section_header("5. HOLT-WINTER MODEL TRAINING")

    print("✓ Training Holt-Winter (Triple Exponential Smoothing) model...")
    print("  Considering additive and multiplicative seasonality...")

    models = {}
    test_results = {}

    # Try different configurations
    configs = [
        ('add', 'add', 24),      # Additive trend, additive seasonal, daily
        ('add', 'mul', 24),      # Additive trend, multiplicative seasonal, daily
        ('mul', 'add', 24),      # Multiplicative trend, additive seasonal, daily
        ('mul', 'mul', 24),      # Multiplicative trend, multiplicative seasonal, daily
        ('add', 'add', 24*7),    # Additive trend, additive seasonal, weekly
        ('add', 'mul', 24*7),    # Additive trend, multiplicative seasonal, weekly
    ]

    best_model = None
    best_aic = np.inf
    best_config = None

    for trend, seasonal, period in configs:
        try:
            print(f"  Trying: trend={trend}, seasonal={seasonal}, period={period}")

            model = ExponentialSmoothing(
                y_train,
                trend=trend,
                seasonal=seasonal,
                seasonal_periods=period,
                damped_trend=True
            )

            fitted_model = model.fit(optimized=True, use_brute=True)

            # Forecast on test set
            test_forecast = fitted_model.forecast(steps=len(y_test))
            test_forecast.index = y_test.index

            rmse_test, mae_test, mape_test = calculate_metrics(y_test, test_forecast)

            models[f"{trend}_{seasonal}_{period}"] = fitted_model
            test_results[f"{trend}_{seasonal}_{period}"] = {
                'forecast': test_forecast,
                'rmse': rmse_test,
                'mae': mae_test,
                'mape': mape_test,
                'aic': fitted_model.aic
            }

            print(f"    AIC: {fitted_model.aic:.2f}, RMSE: {rmse_test:.2f}")

            if fitted_model.aic < best_aic:
                best_aic = fitted_model.aic
                best_model = fitted_model
                best_config = f"{trend}_{seasonal}_{period}"

        except Exception as e:
            print(f"    Failed: {e}")
            continue

    if best_model is None:
        print("✗ All Holt-Winter configurations failed!")
        return None, None, (np.nan, np.nan, np.nan)

    print(f"\n✓ Best Holt-Winter Model: {best_config}")
    print(f"  AIC: {best_aic:.2f}")

    best_results = test_results[best_config]
    print(f"  Test Set Performance:")
    print(f"    RMSE: {best_results['rmse']:.2f}")
    print(f"    MAE: {best_results['mae']:.2f}")
    print(f"    MAPE: {best_results['mape']:.2f}%")

    # Plot test forecast
    plt.figure(figsize=(15, 8))
    plt.plot(y_train.index[-24*7:], y_train[-24*7:],
            label='Last Week of Training', color='blue', alpha=0.7)
    plt.plot(y_test.index, y_test, label='Actual Test Data', color='green')
    plt.plot(best_results['forecast'].index, best_results['forecast'],
            label='Holt-Winter Forecast', color='orange', linestyle='--')
    plt.title(f'Holt-Winter Model ({best_config}) - Test Set Forecast vs Actual')
    plt.xlabel('Date-Time')
    plt.ylabel('Demand')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()

    return best_model, best_results['forecast'], (best_results['rmse'], best_results['mae'], best_results['mape'])

    print("\n✓ Data preprocessing and EDA completed successfully!")
    print("  Ready for model training...")

    # Train ARIMA/SARIMA model
    sarimax_model, sarimax_test_forecast, sarimax_test_metrics = train_arima_sarima_model(
        y_train, exog_train, y_test, exog_test)

    # Train Holt-Winter model
    holt_winter_model, hw_test_forecast, hw_test_metrics = train_holt_winter_model(
        y_train, y_test)

def train_final_models_and_forecast(sarimax_model, holt_winter_model,
                                  train_df, test_df, validation_df,
                                  y_train, exog_train, y_test, exog_test,
                                  y_validation, exog_validation):
    """Train final models on combined data and forecast on validation set"""
    print_section_header("6. FINAL MODEL TRAINING AND VALIDATION FORECASTING")

    # Combine training and test data for final model training
    full_train_df = pd.concat([train_df, test_df])
    y_full_train = full_train_df['demand']
    exog_full_train = full_train_df.drop(columns=['demand'])

    print("✓ Training final models on combined training + test data...")
    print(f"  Combined training data size: {len(y_full_train)} samples")

    # Final SARIMAX model
    print("\n✓ Training final SARIMAX model...")
    try:
        if hasattr(sarimax_model, 'fitted_model'):
            # Manual ARIMA case
            final_sarimax = SARIMAX(
                y=y_full_train,
                exog=exog_full_train,
                order=sarimax_model.order,
                seasonal_order=sarimax_model.seasonal_order,
                enforce_stationarity=False,
                enforce_invertibility=False
            )
        else:
            # pmdarima case
            final_sarimax = SARIMAX(
                y=y_full_train,
                exog=exog_full_train,
                order=sarimax_model.order,
                seasonal_order=sarimax_model.seasonal_order,
                enforce_stationarity=False,
                enforce_invertibility=False
            )

        final_sarimax_fit = final_sarimax.fit(disp=False)
        print("  ✓ Final SARIMAX model trained successfully!")

    except Exception as e:
        print(f"  ✗ Final SARIMAX training failed: {e}")
        final_sarimax_fit = None

    # Final Holt-Winter model
    print("\n✓ Training final Holt-Winter model...")
    try:
        # Get the best configuration from the previous model
        hw_params = holt_winter_model.model.__dict__

        final_hw = ExponentialSmoothing(
            y_full_train,
            trend=hw_params.get('trend'),
            seasonal=hw_params.get('seasonal'),
            seasonal_periods=hw_params.get('seasonal_periods'),
            damped_trend=True
        )
        final_hw_fit = final_hw.fit(optimized=True, use_brute=True)
        print("  ✓ Final Holt-Winter model trained successfully!")

    except Exception as e:
        print(f"  ✗ Final Holt-Winter training failed: {e}")
        final_hw_fit = None

    # Validation forecasting
    print("\n✓ Generating validation forecasts...")

    # 24-hour forecasts
    validation_24h_actual = y_validation.iloc[:24]
    exog_validation_24h = exog_validation.iloc[:24]

    # 168-hour (7-day) forecasts
    validation_168h_actual = y_validation.iloc[:168] if len(y_validation) >= 168 else y_validation
    exog_validation_168h = exog_validation.iloc[:168] if len(exog_validation) >= 168 else exog_validation

    results = {}

    # SARIMAX forecasts
    if final_sarimax_fit is not None:
        try:
            # 24-hour forecast
            sarimax_24h = final_sarimax_fit.predict(
                start=validation_24h_actual.index[0],
                end=validation_24h_actual.index[-1],
                exog=exog_validation_24h
            )
            sarimax_24h.index = validation_24h_actual.index

            # 168-hour forecast
            sarimax_168h = final_sarimax_fit.predict(
                start=validation_168h_actual.index[0],
                end=validation_168h_actual.index[-1],
                exog=exog_validation_168h
            )
            sarimax_168h.index = validation_168h_actual.index

            # Calculate metrics
            sarimax_24h_metrics = calculate_metrics(validation_24h_actual, sarimax_24h)
            sarimax_168h_metrics = calculate_metrics(validation_168h_actual, sarimax_168h)

            results['SARIMAX'] = {
                '24h': {'forecast': sarimax_24h, 'metrics': sarimax_24h_metrics},
                '168h': {'forecast': sarimax_168h, 'metrics': sarimax_168h_metrics}
            }

            print("  ✓ SARIMAX validation forecasts completed")

        except Exception as e:
            print(f"  ✗ SARIMAX validation forecasting failed: {e}")
            results['SARIMAX'] = None

    # Holt-Winter forecasts
    if final_hw_fit is not None:
        try:
            # 24-hour forecast
            hw_24h = final_hw_fit.forecast(steps=24)
            hw_24h.index = validation_24h_actual.index

            # 168-hour forecast
            hw_168h = final_hw_fit.forecast(steps=len(validation_168h_actual))
            hw_168h.index = validation_168h_actual.index

            # Calculate metrics
            hw_24h_metrics = calculate_metrics(validation_24h_actual, hw_24h)
            hw_168h_metrics = calculate_metrics(validation_168h_actual, hw_168h)

            results['Holt-Winter'] = {
                '24h': {'forecast': hw_24h, 'metrics': hw_24h_metrics},
                '168h': {'forecast': hw_168h, 'metrics': hw_168h_metrics}
            }

            print("  ✓ Holt-Winter validation forecasts completed")

        except Exception as e:
            print(f"  ✗ Holt-Winter validation forecasting failed: {e}")
            results['Holt-Winter'] = None

    return results, validation_24h_actual, validation_168h_actual

def compare_models_and_visualize(results, validation_24h_actual, validation_168h_actual,
                               sarimax_test_metrics, hw_test_metrics):
    """Compare models and create comprehensive visualizations"""
    print_section_header("7. MODEL COMPARISON AND RESULTS")

    # Print performance summary
    print("✓ Model Performance Summary:")
    print("-" * 80)
    print(f"{'Model':<15} {'Dataset':<12} {'RMSE':<10} {'MAE':<10} {'MAPE (%)':<10}")
    print("-" * 80)

    # Test set results
    if not np.isnan(sarimax_test_metrics[0]):
        print(f"{'SARIMAX':<15} {'Test':<12} {sarimax_test_metrics[0]:<10.2f} "
              f"{sarimax_test_metrics[1]:<10.2f} {sarimax_test_metrics[2]:<10.2f}")

    if not np.isnan(hw_test_metrics[0]):
        print(f"{'Holt-Winter':<15} {'Test':<12} {hw_test_metrics[0]:<10.2f} "
              f"{hw_test_metrics[1]:<10.2f} {hw_test_metrics[2]:<10.2f}")

    # Validation set results
    for model_name, model_results in results.items():
        if model_results is not None:
            # 24-hour results
            metrics_24h = model_results['24h']['metrics']
            print(f"{model_name:<15} {'Valid-24h':<12} {metrics_24h[0]:<10.2f} "
                  f"{metrics_24h[1]:<10.2f} {metrics_24h[2]:<10.2f}")

            # 168-hour results
            metrics_168h = model_results['168h']['metrics']
            print(f"{model_name:<15} {'Valid-168h':<12} {metrics_168h[0]:<10.2f} "
                  f"{metrics_168h[1]:<10.2f} {metrics_168h[2]:<10.2f}")

    print("-" * 80)

    # Visualization
    print("\n✓ Creating comprehensive visualizations...")

    # 24-hour forecast comparison
    plt.figure(figsize=(15, 10))

    plt.subplot(2, 1, 1)
    plt.plot(validation_24h_actual.index, validation_24h_actual,
            label='Actual', color='black', linewidth=2)

    for model_name, model_results in results.items():
        if model_results is not None:
            forecast_24h = model_results['24h']['forecast']
            plt.plot(forecast_24h.index, forecast_24h,
                    label=f'{model_name} Forecast', linestyle='--', linewidth=2)

    plt.title('24-Hour Validation Forecast Comparison')
    plt.ylabel('Demand')
    plt.legend()
    plt.grid(True)

    # 168-hour forecast comparison
    plt.subplot(2, 1, 2)
    plt.plot(validation_168h_actual.index, validation_168h_actual,
            label='Actual', color='black', linewidth=2)

    for model_name, model_results in results.items():
        if model_results is not None:
            forecast_168h = model_results['168h']['forecast']
            plt.plot(forecast_168h.index, forecast_168h,
                    label=f'{model_name} Forecast', linestyle='--', linewidth=2)

    plt.title('168-Hour (7-Day) Validation Forecast Comparison')
    plt.xlabel('Date-Time')
    plt.ylabel('Demand')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.show()

    # Model performance comparison chart
    if len(results) > 1:
        plt.figure(figsize=(12, 8))

        models = []
        rmse_24h = []
        rmse_168h = []
        mae_24h = []
        mae_168h = []

        for model_name, model_results in results.items():
            if model_results is not None:
                models.append(model_name)
                rmse_24h.append(model_results['24h']['metrics'][0])
                rmse_168h.append(model_results['168h']['metrics'][0])
                mae_24h.append(model_results['24h']['metrics'][1])
                mae_168h.append(model_results['168h']['metrics'][1])

        x = np.arange(len(models))
        width = 0.35

        plt.subplot(2, 1, 1)
        plt.bar(x - width/2, rmse_24h, width, label='24-hour RMSE', alpha=0.8)
        plt.bar(x + width/2, rmse_168h, width, label='168-hour RMSE', alpha=0.8)
        plt.xlabel('Models')
        plt.ylabel('RMSE')
        plt.title('RMSE Comparison')
        plt.xticks(x, models)
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.subplot(2, 1, 2)
        plt.bar(x - width/2, mae_24h, width, label='24-hour MAE', alpha=0.8)
        plt.bar(x + width/2, mae_168h, width, label='168-hour MAE', alpha=0.8)
        plt.xlabel('Models')
        plt.ylabel('MAE')
        plt.title('MAE Comparison')
        plt.xticks(x, models)
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    return results

def generate_final_report(results, sarimax_model, holt_winter_model,
                         sarimax_test_metrics, hw_test_metrics):
    """Generate final comprehensive report"""
    print_section_header("8. FINAL REPORT AND RECOMMENDATIONS")

    print("✓ ELECTRIC DEMAND FORECASTING - FINAL REPORT")
    print("=" * 60)

    print("\n📊 DATASET SUMMARY:")
    print("  • File: electric_demand_1h.csv")
    print("  • Frequency: Hourly data")
    print("  • Target: Electric demand")
    print("  • Exogenous variables: Temperature, WeekDay, Holiday")
    print("  • Data split: 70% train, 20% test, 10% validation")

    print("\n🔧 MODELS IMPLEMENTED:")
    print("  1. SARIMAX (Seasonal ARIMA with eXogenous variables)")
    if sarimax_model is not None:
        print(f"     • Order: {sarimax_model.order}")
        print(f"     • Seasonal order: {sarimax_model.seasonal_order}")
        print(f"     • AIC: {sarimax_model.aic():.2f}")

    print("  2. Holt-Winter (Triple Exponential Smoothing)")
    if holt_winter_model is not None:
        print(f"     • Trend: {holt_winter_model.model.trend}")
        print(f"     • Seasonal: {holt_winter_model.model.seasonal}")
        print(f"     • Seasonal periods: {holt_winter_model.model.seasonal_periods}")

    print("\n📈 PERFORMANCE RESULTS:")

    # Determine best model for each horizon
    best_24h = None
    best_168h = None
    best_24h_rmse = np.inf
    best_168h_rmse = np.inf

    for model_name, model_results in results.items():
        if model_results is not None:
            rmse_24h = model_results['24h']['metrics'][0]
            rmse_168h = model_results['168h']['metrics'][0]

            if rmse_24h < best_24h_rmse:
                best_24h_rmse = rmse_24h
                best_24h = model_name

            if rmse_168h < best_168h_rmse:
                best_168h_rmse = rmse_168h
                best_168h = model_name

    print(f"  🏆 Best model for 24-hour forecast: {best_24h} (RMSE: {best_24h_rmse:.2f})")
    print(f"  🏆 Best model for 168-hour forecast: {best_168h} (RMSE: {best_168h_rmse:.2f})")

    print("\n💡 RECOMMENDATIONS:")
    print("  • Both models capture different aspects of the time series:")
    print("    - SARIMAX: Better for incorporating external factors (temperature, holidays)")
    print("    - Holt-Winter: Better for capturing pure seasonal patterns")
    print("  • Consider ensemble methods for improved accuracy")
    print("  • Monitor model performance and retrain periodically")
    print("  • Outliers were preserved as they represent real-world demand spikes")

    print("\n✅ FORECASTING COMPLETED SUCCESSFULLY!")
    print("  • 24-hour and 168-hour forecasts generated")
    print("  • Models ready for production deployment")
    print("  • Comprehensive evaluation metrics provided")

    print("\n" + "=" * 60)
    print("Thank you for using the Electric Demand Forecasting Program!")
    print("=" * 60)

if __name__ == "__main__":
    print("="*60)
    print("ELECTRIC DEMAND FORECASTING PROGRAM")
    print("ARIMA/SARIMA & Holt-Winter Methods")
    print("="*60)

    # Load and preprocess data
    result = load_and_preprocess_data()
    if result[0] is None:
        exit(1)

    df_processed, df_original, target_col, exog_vars_df, weekday_dummies, holiday_dummy = result

    # Split data
    splits = split_data(df_processed)
    (train_df, test_df, validation_df,
     y_train, exog_train, y_test, exog_test, y_validation, exog_validation) = splits

    # Exploratory data analysis
    decomposition = exploratory_data_analysis(df_processed, y_train)

    print("\n✓ Data preprocessing and EDA completed successfully!")
    print("  Ready for model training...")

    # Train ARIMA/SARIMA model
    sarimax_model, sarimax_test_forecast, sarimax_test_metrics = train_arima_sarima_model(
        y_train, exog_train, y_test, exog_test)

    # Train Holt-Winter model
    holt_winter_model, hw_test_forecast, hw_test_metrics = train_holt_winter_model(
        y_train, y_test)

    # Final model training and validation forecasting
    validation_results, validation_24h_actual, validation_168h_actual = train_final_models_and_forecast(
        sarimax_model, holt_winter_model, train_df, test_df, validation_df,
        y_train, exog_train, y_test, exog_test, y_validation, exog_validation)

    # Model comparison and visualization
    final_results = compare_models_and_visualize(
        validation_results, validation_24h_actual, validation_168h_actual,
        sarimax_test_metrics, hw_test_metrics)

    # Generate final report
    generate_final_report(final_results, sarimax_model, holt_winter_model,
                         sarimax_test_metrics, hw_test_metrics)
